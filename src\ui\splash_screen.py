"""
Splash screen for SofreTrack Pro application.

This module provides a professional splash screen that loads dependencies
while displaying progress to the user, using the same design language as the main application.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import logging
from pathlib import Path
import os

from config.constants import COLORS, UIConfig, AppInfo
from utils.file_utils import get_icon_path

logger = logging.getLogger(__name__)


class SplashScreen:
    """Professional splash screen with dependency loading and progress tracking."""
    
    def __init__(self, on_complete_callback=None):
        """
        Initialize the splash screen.
        
        Args:
            on_complete_callback: Function to call when loading is complete
        """
        self.on_complete_callback = on_complete_callback
        self.root = None
        self.progress_var = None
        self.status_var = None
        self.loading_complete = False
        self.error_occurred = False
        self.error_message = ""
        
        # Loading steps for progress tracking
        self.loading_steps = [
            ("Initialisation de l'environnement...", self._init_environment),
            ("Chargement Des Bibliothèques...", self._load_pandas),
            ("Chargement Des Bibliothèques...", self._load_pil),
            ("Chargement Des Bibliothèques...", self._load_openpyxl),
            ("Configuration des modules...", self._setup_modules),
            ("Finalisation...", self._finalize_loading)
        ]
        
        self.current_step = 0
        self.total_steps = len(self.loading_steps)
        
        self.logger = logging.getLogger(__name__)
    
    def show(self):
        """Display the splash screen and start loading."""
        self._create_splash_window()
        self._start_loading()
        self.root.mainloop()
    
    def _create_splash_window(self):
        """Create and configure the splash screen window."""
        self.root = tk.Tk()
        self.root.title("SofreTrack Pro")
        
        # Window configuration
        window_width = 500
        window_height = 350
        
        # Center the window
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.resizable(False, False)
        self.root.overrideredirect(True)  # Remove window decorations
        self.root.configure(bg=COLORS['WHITE'])
        
        # Set window icon
        try:
            icon_path = get_icon_path()
            if icon_path and os.path.exists(icon_path):
                # For splash screen, we'll load PIL directly since it's part of our loading process
                pass  # We'll handle icon loading in the main content
        except Exception as e:
            self.logger.warning(f"Could not set splash screen icon: {e}")
        
        self._create_splash_content()
        
        # Add subtle shadow effect
        self.root.wm_attributes("-topmost", True)
    
    def _create_splash_content(self):
        """Create the splash screen content with Sofrecom design language."""
        # Main container with border
        main_frame = tk.Frame(
            self.root,
            bg=COLORS['WHITE'],
            relief='solid',
            bd=1,
            highlightbackground=COLORS['BORDER'],
            highlightthickness=1
        )
        main_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Header section with gradient-like effect
        header_frame = tk.Frame(main_frame, bg=COLORS['PRIMARY'], height=80)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # App icon and title in header
        header_content = tk.Frame(header_frame, bg=COLORS['PRIMARY'])
        header_content.pack(expand=True, fill='both')
        
        # Try to load and display app icon
        self._add_app_icon(header_content)
        
        # App title
        title_label = tk.Label(
            header_content,
            text="SofreTrack Pro",
            font=("Segoe UI", 18, "bold"),
            fg=COLORS['WHITE'],
            bg=COLORS['PRIMARY']
        )
        title_label.pack(pady=(10, 5))
        
        # Subtitle
        subtitle_label = tk.Label(
            header_content,
            text="Solutions de traitement et génération de données",
            font=UIConfig.FONT_SUBTITLE,
            fg=COLORS['WHITE'],
            bg=COLORS['PRIMARY']
        )
        subtitle_label.pack()
        
        # Content area
        content_frame = tk.Frame(main_frame, bg=COLORS['WHITE'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 30))
        
        # Loading status
        self.status_var = tk.StringVar(value="Initialisation...")
        status_label = tk.Label(
            content_frame,
            textvariable=self.status_var,
            font=UIConfig.FONT_SUBHEADER,
            fg=COLORS['INFO'],
            bg=COLORS['WHITE']
        )
        status_label.pack(pady=(20, 10))
        
        # Progress bar with Sofrecom styling
        progress_frame = tk.Frame(content_frame, bg=COLORS['WHITE'])
        progress_frame.pack(fill='x', pady=(0, 20))
        
        # Custom progress bar style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure(
            "Sofrecom.Horizontal.TProgressbar",
            background=COLORS['PRIMARY'],
            troughcolor=COLORS['LIGHT'],
            borderwidth=0,
            lightcolor=COLORS['PRIMARY'],
            darkcolor=COLORS['PRIMARY']
        )
        
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            style="Sofrecom.Horizontal.TProgressbar",
            length=400
        )
        progress_bar.pack()
        
        # Progress percentage
        self.progress_text_var = tk.StringVar(value="0%")
        progress_text = tk.Label(
            content_frame,
            textvariable=self.progress_text_var,
            font=UIConfig.FONT_SMALL,
            fg=COLORS['TEXT_SECONDARY'],
            bg=COLORS['WHITE']
        )
        progress_text.pack(pady=(5, 0))
        
        # Footer with version and copyright
        footer_frame = tk.Frame(main_frame, bg=COLORS['LIGHT'], height=40)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)
        
        footer_content = tk.Frame(footer_frame, bg=COLORS['LIGHT'])
        footer_content.pack(expand=True, fill='both')
        
        version_label = tk.Label(
            footer_content,
            text=f"Version {AppInfo.VERSION} - {AppInfo.COPYRIGHT}",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['TEXT_MUTED'],
            bg=COLORS['LIGHT']
        )
        version_label.pack(pady=10)
    
    def _add_app_icon(self, parent):
        """Add the application icon to the splash screen."""
        try:
            icon_path = get_icon_path()
            if icon_path and os.path.exists(icon_path):
                # Create icon container
                icon_frame = tk.Frame(parent, bg=COLORS['PRIMARY'])
                icon_frame.pack(side='left', padx=(20, 10), pady=10)

                # Try to load icon immediately with tkinter PhotoImage for PNG files
                icon_loaded = False
                if icon_path.lower().endswith('.png'):
                    try:
                        # Try to load PNG directly with tkinter (works for some PNG files)
                        photo = tk.PhotoImage(file=icon_path)
                        # Subsample to reduce size (better quality than resize for pixel art)
                        photo = photo.subsample(max(1, photo.width() // 40), max(1, photo.height() // 40))

                        icon_placeholder = tk.Label(
                            icon_frame,
                            image=photo,
                            bg=COLORS['PRIMARY'],
                            width=40,
                            height=40
                        )
                        icon_placeholder.image = photo  # Keep reference
                        icon_placeholder.pack()
                        icon_loaded = True
                    except Exception as e:
                        self.logger.debug(f"Could not load PNG with tkinter: {e}")

                # If icon not loaded, use emoji placeholder
                if not icon_loaded:
                    icon_placeholder = tk.Label(
                        icon_frame,
                        text="🏢",  # Building emoji placeholder
                        font=("Segoe UI", 28),
                        fg=COLORS['WHITE'],
                        bg=COLORS['PRIMARY'],
                        width=3,
                        height=2
                    )
                    icon_placeholder.pack()

                # Store reference for later icon loading with PIL
                self.icon_placeholder = icon_placeholder
                self.icon_path = icon_path

        except Exception as e:
            self.logger.warning(f"Could not prepare app icon: {e}")
    
    def _start_loading(self):
        """Start the dependency loading process in a separate thread."""
        loading_thread = threading.Thread(target=self._load_dependencies, daemon=True)
        loading_thread.start()
    
    def _load_dependencies(self):
        """Load all dependencies with progress updates."""
        try:
            for i, (status_text, load_function) in enumerate(self.loading_steps):
                self.current_step = i
                
                # Update status
                self.root.after(0, lambda text=status_text: self.status_var.set(text))
                
                # Update progress
                progress = (i / self.total_steps) * 100
                self.root.after(0, lambda p=progress: self._update_progress(p))
                
                # Execute loading step
                try:
                    load_function()
                    # Small delay to show progress
                    time.sleep(0.3)
                except Exception as e:
                    self.logger.error(f"Error in loading step '{status_text}': {e}")
                    self.error_occurred = True
                    self.error_message = f"Erreur lors du chargement: {str(e)}"
                    break
            
            # Final progress update
            if not self.error_occurred:
                self.root.after(0, lambda: self._update_progress(100))
                self.root.after(0, lambda: self.status_var.set("Chargement terminé!"))
                time.sleep(0.5)
                self.loading_complete = True
            
            # Close splash screen and start main app
            self.root.after(100, self._finish_loading)
            
        except Exception as e:
            self.logger.error(f"Critical error during loading: {e}")
            self.error_occurred = True
            self.error_message = f"Erreur critique: {str(e)}"
            self.root.after(0, self._show_error)
    
    def _update_progress(self, value):
        """Update the progress bar and percentage text."""
        self.progress_var.set(value)
        self.progress_text_var.set(f"{int(value)}%")
        self.root.update_idletasks()
    
    def _finish_loading(self):
        """Finish loading and close splash screen."""
        if self.error_occurred:
            self._show_error()
        else:
            self.root.destroy()
            if self.on_complete_callback:
                self.on_complete_callback()
    
    def _show_error(self):
        """Show error message and close application."""
        import tkinter.messagebox as messagebox
        messagebox.showerror(
            "Erreur de chargement",
            f"Impossible de charger l'application:\n\n{self.error_message}\n\nVeuillez vérifier que toutes les dépendances sont installées."
        )
        self.root.destroy()

    # Loading step implementations
    def _init_environment(self):
        """Initialize the application environment."""
        from utils.logging_config import setup_logging, configure_third_party_loggers
        setup_logging(log_level="INFO", log_to_file=True)
        configure_third_party_loggers()

        # Log application startup information
        logger = logging.getLogger(__name__)
        logger.info("=" * 60)
        logger.info(f"Starting {AppInfo.DESCRIPTION}")
        logger.info(f"Version: {AppInfo.VERSION}")
        logger.info(f"Author: {AppInfo.AUTHOR}")
        logger.info("=" * 60)

    def _load_pandas(self):
        """Load pandas dependency."""
        import pandas as pd
        # Store in global cache for lazy imports
        from utils.lazy_imports import _import_cache
        _import_cache['pandas'] = pd

    def _load_pil(self):
        """Load PIL dependency and update icon if possible."""
        from PIL import Image, ImageTk
        # Store in global cache
        from utils.lazy_imports import _import_cache
        _import_cache['PIL_Image'] = Image
        _import_cache['PIL_ImageTk'] = ImageTk

        # Try to update the icon now that PIL is loaded
        self._update_icon_with_pil(Image, ImageTk)

    def _load_openpyxl(self):
        """Load openpyxl dependency."""
        import openpyxl
        # Store in global cache
        from utils.lazy_imports import _import_cache
        _import_cache['openpyxl'] = openpyxl

    def _setup_modules(self):
        """Set up application modules."""
        # Pre-load any other necessary modules
        pass

    def _finalize_loading(self):
        """Finalize the loading process."""
        # Any final setup steps
        pass

    def _update_icon_with_pil(self, Image, ImageTk):
        """Update the icon placeholder with the actual icon using PIL."""
        try:
            if hasattr(self, 'icon_placeholder') and hasattr(self, 'icon_path'):
                # Only update if the current icon is an emoji (text)
                current_config = self.icon_placeholder.cget('text')
                if current_config:  # If there's text, it means we're using emoji placeholder

                    # Load the icon with high quality
                    pil_image = Image.open(self.icon_path)

                    # Target size for crisp display
                    target_size = 40

                    # Get original size and calculate best fit while maintaining aspect ratio
                    original_width, original_height = pil_image.size

                    # Calculate scaling to fit within target size while maintaining aspect ratio
                    scale = min(target_size / original_width, target_size / original_height)
                    new_width = int(original_width * scale)
                    new_height = int(original_height * scale)

                    # Use high-quality resampling for crisp icon
                    # LANCZOS provides the best quality for downscaling
                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # Convert to PhotoImage
                    photo = ImageTk.PhotoImage(pil_image)

                    # Update the placeholder - clear text and set image
                    self.icon_placeholder.configure(
                        image=photo,
                        text="",
                        width=target_size,
                        height=target_size
                    )
                    self.icon_placeholder.image = photo  # Keep a reference

                    self.logger.debug(f"Updated icon with PIL: {new_width}x{new_height}")

        except Exception as e:
            self.logger.warning(f"Could not update icon with PIL: {e}")
